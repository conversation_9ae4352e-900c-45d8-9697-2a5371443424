import { PrismaClient } from '@prisma/client';
import { logger } from '../../utils/logger';
import { ForumNotificationService } from './forumNotificationService';
import { GameNotificationService } from './gameNotificationService';
import { SocketNotificationService } from './socketNotificationService';

const prisma = new PrismaClient();

export interface NotificationFilters {
  type?: string;
  priority?: 'low' | 'medium' | 'high';
  isRead?: boolean;
  startDate?: Date;
  endDate?: Date;
}

export interface NotificationPreferences {
  emailNotifications: boolean;
  pushNotifications: boolean;
  forumNotifications: boolean;
  gameNotifications: boolean;
  mentionNotifications: boolean;
}

export class NotificationService {
  // Get notifications for a user with pagination and filters
  static async getUserNotifications(
    userId: number,
    page: number = 1,
    limit: number = 20,
    filters: NotificationFilters = {}
  ) {
    try {
      const skip = (page - 1) * limit;

      const whereClause: any = {
        staffId: userId,
      };

      if (filters.type) {
        whereClause.type = filters.type;
      }

      if (filters.priority) {
        whereClause.priority = filters.priority;
      }

      if (filters.isRead !== undefined) {
        whereClause.isRead = filters.isRead;
      }

      if (filters.startDate || filters.endDate) {
        whereClause.createdAt = {};
        if (filters.startDate) {
          whereClause.createdAt.gte = filters.startDate;
        }
        if (filters.endDate) {
          whereClause.createdAt.lte = filters.endDate;
        }
      }

      const [notifications, total] = await Promise.all([
        prisma.notification.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
          select: {
            id: true,
            type: true,
            title: true,
            message: true,
            data: true,
            priority: true,
            isRead: true,
            createdAt: true,
          },
        }),
        prisma.notification.count({ where: whereClause }),
      ]);

      const pages = Math.ceil(total / limit);

      return {
        notifications: notifications.map((notification) => ({
          ...notification,
          data: notification.data ? JSON.parse(notification.data) : null,
        })),
        pagination: {
          page,
          limit,
          total,
          pages,
          hasNext: page < pages,
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      logger.error('Error getting user notifications:', error);
      throw error;
    }
  }

  // Mark notifications as read
  static async markAsRead(userId: number, notificationIds: string[]) {
    try {
      const result = await prisma.notification.updateMany({
        where: {
          id: { in: notificationIds },
          staffId: userId,
        },
        data: {
          isRead: true,
          readAt: new Date(),
        },
      });

      // Emit socket notification for read status update
      SocketNotificationService.notifyUser(userId, 'notifications_marked_read', {
        title: 'Notifications Updated',
        message: `${result.count} notifications marked as read`,
        type: 'info',
        data: { count: result.count, notificationIds },
      });

      logger.info(
        `Marked ${result.count} notifications as read for user ${userId}`
      );
      return result;
    } catch (error) {
      logger.error('Error marking notifications as read:', error);
      throw error;
    }
  }

  // Mark all notifications as read for a user
  static async markAllAsRead(userId: number) {
    try {
      const result = await prisma.notification.updateMany({
        where: {
          staffId: userId,
          isRead: false,
        },
        data: {
          isRead: true,
          readAt: new Date(),
        },
      });

      // Emit socket notification for all read
      SocketNotificationService.notifyUser(userId, 'all_notifications_marked_read', {
        title: 'All Notifications Read',
        message: `All ${result.count} notifications marked as read`,
        type: 'success',
        data: { count: result.count },
      });

      logger.info(
        `Marked all ${result.count} notifications as read for user ${userId}`
      );
      return result;
    } catch (error) {
      logger.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  // Get unread notification count
  static async getUnreadCount(userId: number) {
    try {
      const count = await prisma.notification.count({
        where: {
          staffId: userId,
          isRead: false,
        },
      });

      return { count };
    } catch (error) {
      logger.error('Error getting unread notification count:', error);
      throw error;
    }
  }

  // Delete notifications
  static async deleteNotifications(userId: number, notificationIds: string[]) {
    try {
      const result = await prisma.notification.deleteMany({
        where: {
          id: { in: notificationIds },
          staffId: userId,
        },
      });

      logger.info(`Deleted ${result.count} notifications for user ${userId}`);
      return result;
    } catch (error) {
      logger.error('Error deleting notifications:', error);
      throw error;
    }
  }

  // Get notification preferences for a user
  static async getNotificationPreferences(
    userId: number
  ): Promise<NotificationPreferences> {
    try {
      const preferences = await prisma.notificationPreference.findUnique({
        where: { staffId: userId },
      });

      if (!preferences) {
        // Return default preferences
        return {
          emailNotifications: true,
          pushNotifications: true,
          forumNotifications: true,
          gameNotifications: true,
          mentionNotifications: true,
        };
      }

      return {
        emailNotifications: preferences.emailNotifications,
        pushNotifications: preferences.pushNotifications,
        forumNotifications: preferences.forumNotifications,
        gameNotifications: preferences.gameNotifications,
        mentionNotifications: preferences.mentionNotifications,
      };
    } catch (error) {
      logger.error('Error getting notification preferences:', error);
      throw error;
    }
  }

  // Update notification preferences for a user
  static async updateNotificationPreferences(
    userId: number,
    preferences: Partial<NotificationPreferences>
  ) {
    try {
      const result = await prisma.notificationPreference.upsert({
        where: { staffId: userId },
        update: preferences,
        create: {
          staffId: userId,
          ...preferences,
        },
      });

      logger.info(`Updated notification preferences for user ${userId}`);
      return result;
    } catch (error) {
      logger.error('Error updating notification preferences:', error);
      throw error;
    }
  }

  // Get notification statistics
  static async getNotificationStats(userId: number) {
    try {
      const [total, unread, byType, byPriority] = await Promise.all([
        prisma.notification.count({
          where: { staffId: userId },
        }),
        prisma.notification.count({
          where: { staffId: userId, isRead: false },
        }),
        prisma.notification.groupBy({
          by: ['type'],
          where: { staffId: userId },
          _count: { type: true },
        }),
        prisma.notification.groupBy({
          by: ['priority'],
          where: { staffId: userId },
          _count: { priority: true },
        }),
      ]);

      return {
        total,
        unread,
        read: total - unread,
        byType: byType.map((item) => ({
          type: item.type,
          count: item._count.type,
        })),
        byPriority: byPriority.map((item) => ({
          priority: item.priority,
          count: item._count.priority,
        })),
      };
    } catch (error) {
      logger.error('Error getting notification statistics:', error);
      throw error;
    }
  }

  // Clean up old notifications (older than 30 days)
  static async cleanupOldNotifications() {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const result = await prisma.notification.deleteMany({
        where: {
          createdAt: {
            lt: thirtyDaysAgo,
          },
          isRead: true,
        },
      });

      logger.info(`Cleaned up ${result.count} old notifications`);
      return result;
    } catch (error) {
      logger.error('Error cleaning up old notifications:', error);
      throw error;
    }
  }

  // Get recent activity for dashboard
  static async getRecentActivity(userId: number, limit: number = 10) {
    try {
      const notifications = await prisma.notification.findMany({
        where: { staffId: userId },
        orderBy: { createdAt: 'desc' },
        take: limit,
        select: {
          id: true,
          type: true,
          title: true,
          message: true,
          data: true,
          priority: true,
          isRead: true,
          createdAt: true,
        },
      });

      return notifications.map((notification) => ({
        ...notification,
        data: notification.data ? JSON.parse(notification.data) : null,
      }));
    } catch (error) {
      logger.error('Error getting recent activity:', error);
      throw error;
    }
  }

  // Bulk operations for admin
  static async sendBulkNotification(
    userIds: number[],
    title: string,
    message: string,
    type: string = 'system',
    priority: 'low' | 'medium' | 'high' = 'medium'
  ) {
    try {
      const notifications = userIds.map((userId) => ({
        staffId: userId,
        type,
        title,
        message,
        priority,
        isRead: false,
      }));

      const result = await prisma.notification.createMany({
        data: notifications,
      });

      // Emit socket notifications to all users
      SocketNotificationService.notifyMultipleUsers(userIds, 'new_notification', {
        title,
        message,
        type: priority === 'high' ? 'warning' : 'info',
        data: { type, priority },
      });

      logger.info(`Sent bulk notification to ${userIds.length} users`);
      return result;
    } catch (error) {
      logger.error('Error sending bulk notification:', error);
      throw error;
    }
  }

  // Create notification with socket emission
  static async createNotificationWithSocket(
    userId: number,
    title: string,
    message: string,
    type: string = 'system',
    priority: 'low' | 'medium' | 'high' = 'medium',
    data?: any
  ) {
    try {
      // Create database notification
      const notification = await prisma.notification.create({
        data: {
          staffId: userId,
          type,
          title,
          message,
          priority,
          data: data ? JSON.stringify(data) : null,
          isRead: false,
        },
      });

      // Emit socket notification
      SocketNotificationService.notifyUser(userId, 'new_notification', {
        title,
        message,
        type: priority === 'high' ? 'warning' : priority === 'low' ? 'info' : 'success',
        data: {
          notificationId: notification.id,
          type,
          priority,
          ...data,
        },
      });

      logger.info(`Notification created and sent to user ${userId}: ${title}`);
      return notification;
    } catch (error) {
      logger.error('Error creating notification with socket:', error);
      throw error;
    }
  }

  // Export socket notification service
  static socket = SocketNotificationService;

  // Export forum notification methods
  static forum = ForumNotificationService;

  // Export game notification methods
  static game = GameNotificationService;
}
