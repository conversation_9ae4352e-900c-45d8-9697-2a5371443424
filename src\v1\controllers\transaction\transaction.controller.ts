import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { transactionService } from '../../services/transaction';

const ListAllTransactions = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    transactionService.getAllTransactions,
    req.query,
    res,
    staffId
  );
};
const ListAllStaffTransactions = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    transactionService.getStaffTransactions,
    req.query,
    res,
    staffId
  );
};

const withdrawalRequest = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    transactionService.staffWithdrawal,
    req.body,
    res,
    staffId
  );
};

const transferFunds = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    transactionService.staffFundTransfer,
    req.body,
    res,
    staffId
  );
};

const withdrawalRequestUpdate = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    transactionService.staffFundWithdrawalUpdate,
    req.body,
    res,
    staffId
  );
};

const SendVoucherHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    transactionService.sendVoucher,
    req.body,
    res,
    staffId
  );
};

export const transactionControllers = {
  ListAllTransactions,
  ListAllStaffTransactions,
  withdrawalRequest,
  withdrawalRequestUpdate,
  transferFunds,
  SendVoucherHandler,
};
