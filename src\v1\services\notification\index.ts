import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { logger } from '../../utils/logger';
import {
  getCache,
  setCache,
  deleteCache,
  deleteCacheByPattern,
} from '../../utils/cache';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
// import { ForumNotificationService } from './forumNotificationService';
// import { GameNotificationService } from './gameNotificationService';
import { SocketNotificationService } from './socketNotificationService';

export interface NotificationFilters {
  type?: string;
  priority?: 'low' | 'medium' | 'high';
  isRead?: boolean;
  startDate?: Date;
  endDate?: Date;
}

export interface NotificationPreferences {
  emailNotifications: boolean;
  pushNotifications: boolean;
  forumNotifications: boolean;
  gameNotifications: boolean;
  mentionNotifications: boolean;
}

// Helper function to clear all notification-related caches
export const clearNotificationCaches = async (): Promise<void> => {
  await deleteCacheByPattern('notifications:*');
};

export const notificationService = {
  // Get notifications for a user with pagination and filters
  getUserNotifications: async (
    staffId: number,
    query: any
  ) => {
    const page = parseInt(query.page) || 1;
    const limit = parseInt(query.limit) || 20;
    const skip = (page - 1) * limit;

    const whereClause: any = {
      staffId,
    };

    if (query.type) {
      whereClause.type = query.type;
    }

    if (query.priority) {
      whereClause.priority = query.priority;
    }

    if (query.isRead !== undefined) {
      whereClause.isRead = query.isRead === 'true';
    }

    if (query.startDate || query.endDate) {
      whereClause.createdAt = {};
      if (query.startDate) {
        whereClause.createdAt.gte = new Date(query.startDate);
      }
      if (query.endDate) {
        whereClause.createdAt.lte = new Date(query.endDate);
      }
    }

    const [notifications, total] = await Promise.all([
      db.notification.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
        select: {
          id: true,
          type: true,
          title: true,
          message: true,
          data: true,
          priority: true,
          isRead: true,
          createdAt: true,
        },
      }),
      db.notification.count({ where: whereClause }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      notifications: notifications.map((notification) => ({
        ...notification,
        data: notification.data ? JSON.parse(notification.data) : null,
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    };
  },

  // Mark notifications as read
  markAsRead: async (staffId: number, notificationId: string) => {

    const result = await db.notification.updateMany({
      where: {
        id: notificationId,
        staffId,
      },
      data: {
        isRead: true,
        readAt: new Date(),
      },
    });

    // Emit socket notification for read status update
    SocketNotificationService.notifyUser(staffId, 'notifications_marked_read', {
      title: 'Notifications Updated',
      message: `${result.count} notifications marked as read`,
      type: 'info',
      data: { count: result.count, notificationId },
    });

    logger.info(`Marked ${result.count} notifications as read for user ${staffId}`);
    return result;
  },

  // Mark all notifications as read for a user
  markAllAsRead: async (staffId: number) => {
    const result = await db.notification.updateMany({
      where: {
        staffId,
        isRead: false,
      },
      data: {
        isRead: true,
        readAt: new Date(),
      },
    });

    // Emit socket notification for all read
    SocketNotificationService.notifyUser(staffId, 'all_notifications_marked_read', {
      title: 'All Notifications Read',
      message: `All ${result.count} notifications marked as read`,
      type: 'success',
      data: { count: result.count },
    });

    logger.info(`Marked all ${result.count} notifications as read for user ${staffId}`);
    return result;
  },

  // Get unread notification count
  getUnreadCount: async (staffId: number) => {
    const count = await db.notification.count({
      where: {
        staffId,
        isRead: false,
      },
    });

    return { count };
  },

  // Delete notifications
  deleteNotifications: async (staffId: number, notificationId: string) => {
    const result = await db.notification.delete({
      where: {
        id: notificationId,
        staffId,
      },
    });

    logger.info(`Deleted ${result.message} notifications for user ${staffId}`);
    return result;
  },

    deleteAllNotifications: async (staffId: number) => {
    const result = await db.notification.deleteMany({
      where: {
        staffId,
      },
    });

    logger.info(`Deleted ${result.count} notifications for user ${staffId}`);
    return result;
  },



  // Get notification preferences for a user
  getNotificationPreferences: async (staffId: number): Promise<NotificationPreferences> => {
    const preferences = await db.notificationPreference.findUnique({
      where: { staffId },
    });

    if (!preferences) {
      // Return default preferences
      return {
        emailNotifications: true,
        pushNotifications: true,
        forumNotifications: true,
        gameNotifications: true,
        mentionNotifications: true,
      };
    }

    return {
      emailNotifications: preferences.emailNotifications,
      pushNotifications: preferences.pushNotifications,
      forumNotifications: preferences.forumNotifications,
      gameNotifications: preferences.gameNotifications,
      mentionNotifications: preferences.mentionNotifications,
    };
  },

  // Update notification preferences for a user
  updateNotificationPreferences: async (
    staffId: number,
    reqBody: Partial<NotificationPreferences>
  ) => {
    // Validate preferences
    const validKeys = [
      'emailNotifications',
      'pushNotifications',
      'forumNotifications',
      'gameNotifications',
      'mentionNotifications',
    ];

    const filteredPreferences: any = {};
    for (const key of validKeys) {
      if (key in reqBody && typeof reqBody[key as keyof NotificationPreferences] === 'boolean') {
        filteredPreferences[key] = reqBody[key as keyof NotificationPreferences];
      }
    }

    if (Object.keys(filteredPreferences).length === 0) {
      throw new HttpError('No valid preferences provided', 400);
    }

    const result = await db.notificationPreference.upsert({
      where: { staffId },
      update: filteredPreferences,
      create: {
        staffId,
        ...filteredPreferences,
      },
    });

    logger.info(`Updated notification preferences for user ${staffId}`);
    return result;
  },

  // Get notification statistics
  getNotificationStats: async (staffId: number) => {
    const [total, unread, byType, byPriority] = await Promise.all([
      db.notification.count({
        where: { staffId },
      }),
      db.notification.count({
        where: { staffId, isRead: false },
      }),
      db.notification.groupBy({
        by: ['type'],
        where: { staffId },
        _count: { type: true },
      }),
      db.notification.groupBy({
        by: ['priority'],
        where: { staffId },
        _count: { priority: true },
      }),
    ]);

    return {
      total,
      unread,
      read: total - unread,
      byType: byType.map((item) => ({
        type: item.type,
        count: item._count.type,
      })),
      byPriority: byPriority.map((item) => ({
        priority: item.priority,
        count: item._count.priority,
      })),
    };
  },

  // Clean up old notifications (older than 30 days)
  cleanupOldNotifications: async (staffId: number) => {
    await staffHasPermission(staffId, PERMISSIONS.STAFF_EDIT);

    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const result = await db.notification.deleteMany({
      where: {
        createdAt: {
          lt: thirtyDaysAgo,
        },
        isRead: true,
      },
    });

    logger.info(`Cleaned up ${result.count} old notifications`);
    return result;
  },

  // Get recent activity for dashboard
  getRecentActivity: async (staffId: number, query: any) => {
    const limit = parseInt(query.limit) || 10;

    const notifications = await db.notification.findMany({
      where: { staffId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      select: {
        id: true,
        type: true,
        title: true,
        message: true,
        data: true,
        priority: true,
        isRead: true,
        createdAt: true,
      },
    });

    return notifications.map((notification) => ({
      ...notification,
      data: notification.data ? JSON.parse(notification.data) : null,
    }));
  },

  // Create notification with socket emission
  createNotificationWithSocket: async (
    userId: number,
    title: string,
    message: string,
    type: string = 'system',
    priority: 'low' | 'medium' | 'high' = 'medium',
    data?: any
  ) => {
    // Create database notification
    const notification = await db.notification.create({
      data: {
        staffId: userId,
        type,
        title,
        message,
        priority,
        data: data ? JSON.stringify(data) : null,
        isRead: false,
      },
    });

    // Emit socket notification
    SocketNotificationService.notifyUser(userId, 'new_notification', {
      title,
      message,
      type: priority === 'high' ? 'warning' : priority === 'low' ? 'info' : 'success',
      data: {
        notificationId: notification.id,
        type,
        priority,
        ...data,
      },
    });

    logger.info(`Notification created and sent to user ${userId}: ${title}`);
    return notification;
  },

  // Bulk operations for admin
  sendBulkNotification: async (
    staffId: number,
    reqBody: {
      userIds: number[];
      title: string;
      message: string;
      type?: string;
      priority?: 'low' | 'medium' | 'high';
    }
  ) => {
    await staffHasPermission(staffId, PERMISSIONS.STAFF_EDIT);

    const { userIds, title, message, type = 'system', priority = 'medium' } = reqBody;

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      throw new HttpError('User IDs are required and must be a non-empty array', 400);
    }

    if (!title || !message) {
      throw new HttpError('Title and message are required', 400);
    }

    const notifications = userIds.map((userId) => ({
      staffId: userId,
      type,
      title,
      message,
      priority,
      isRead: false,
    }));

    const result = await db.notification.createMany({
      data: notifications,
    });

    // Emit socket notifications to all users
    SocketNotificationService.notifyMultipleUsers(userIds, 'new_notification', {
      title,
      message,
      type: priority === 'high' ? 'warning' : 'info',
      data: { type, priority },
    });

    logger.info(`Sent bulk notification to ${userIds.length} users`);
    return result;
  },
};
