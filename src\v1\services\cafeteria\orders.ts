import { db, decimal } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { createDateFilter } from '../../utils/util';
import { OrderType, PaymentType } from '../../utils/util';
import dayjs from 'dayjs';
import {
  staffHasPermission,
  PERMISSIONS,
  createStaffAuthHelper,
} from '../../utils/permission';
import { formatString } from '../../utils/stringFormatter';
import { createCSVExport, CSV_CONFIGS } from '../../utils/csvExport';
import { Prisma } from '@prisma/client';
import { generateOrderNumber } from '../../utils/util';
import { sendToUser, broadcast } from '../socket';
import { notificationService } from '../notification';

const getCafeteriaSystemAccount = async () => {
  return await db.systemAccount.findFirst({
    where: {
      type: 'CAFETERIA',
    },
    include: {
      account: true,
    },
  });
};

const getMainSystemAccount = async () => {
  return await db.systemAccount.findFirst({
    where: {
      type: 'COMPANY',
    },
    include: {
      account: true,
    },
  });
};

export const orderService = {
  checkStaffPayment: async (staffId: any, reqBody: any) => {
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(
      PERMISSIONS.CAFETERIA_POS_ACCESS
    );
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }

    const { code, amount } = reqBody;
    const staffCode = formatString.trimString(code);

    const checkStaff = await db.staff.findUnique({
      where: { staffCode },
      include: { location: true, account: true },
    });

    if (!checkStaff || !checkStaff.isActive) {
      throw new HttpError('Staff does not exist', 400);
    }
    if (checkStaff.locked) {
      throw new HttpError('Staff account is locked', 403);
    }

    const requesterLocationId = await auth.getLocationId();

    const requesterLocation = await db.location.findUnique({
      where: { id: Number(requesterLocationId) },
    });

    const sameGroup =
      requesterLocation?.regionId === checkStaff.location.regionId;

    if (!sameGroup) {
      throw new HttpError(
        'You are not authorized to access this cafeteria',
        403
      );
    }

    const { creditLimit, wallet, monthlyCreditUsed, mealVoucher } = checkStaff;
    const decimalAmount = new decimal(amount);

    const isCreditSufficient = creditLimit
      .sub(monthlyCreditUsed)
      .gte(decimalAmount);

    const isWalletSufficient = new decimal(wallet).gte(decimalAmount);

    const isMealVoucherSufficient = mealVoucher.gte(decimalAmount);

    return {
      creditLimit: isCreditSufficient,
      wallet: isWalletSufficient,
      mealVoucher: isMealVoucherSufficient,
    };
  },
  createOrder: async (staffId: any, reqBody: any) => {
    const { cart, code, type, method, totalAmount, ...order } = reqBody;
    const auth = createStaffAuthHelper(staffId);
    const amount = new decimal(totalAmount);

    const canManage = await auth.hasPermission(
      PERMISSIONS.CAFETERIA_POS_ACCESS
    );
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }

    const requesterLocationId = await auth.getLocationId();

    const requesterLocation = await db.location.findUnique({
      where: { id: Number(requesterLocationId) },
    });

    if (!requesterLocation) {
      throw new HttpError('Invalid location', 400);
    }

    if (!PaymentType.includes(method.toUpperCase())) {
      throw new HttpError('Not an acceptable payment method', 403);
    }

    if (!OrderType.includes(type.toUpperCase())) {
      throw new HttpError('Not an acceptable order type', 403);
    }

    const cafeteriaSystemAccount = await getCafeteriaSystemAccount();

    const orderNumber = `ORD-0${requesterLocation.id}${generateOrderNumber()}`;
    const operations: any[] = [];

    let staffCode = null;
    let staffRecord = null;

    if (code) {
      staffCode = formatString.trimString(code);

      staffRecord = await db.staff.findUnique({
        where: { staffCode },
        include: { location: true, account: true },
      });

      if (!staffRecord || !staffRecord.isActive) {
        throw new HttpError('Staff does not exist', 400);
      }

      const sameGroup =
        requesterLocation.regionId === staffRecord.location.regionId;
      if (!sameGroup) {
        throw new HttpError(
          'You are not authorized to access this cafeteria',
          403
        );
      }

      const methodUpper = method.toUpperCase();

      if (methodUpper === 'CREDIT') {
        operations.push(
          db.staff.update({
            where: { staffCode },
            data: {
              monthlyCreditUsed: {
                increment: amount,
              },
            },
          })
        );
      }

      if (methodUpper === 'WALLET') {
        operations.push(
          db.staff.update({
            where: { staffCode },
            data: {
              wallet: {
                decrement: amount,
              },
            },
          })
        );
      }

      if (methodUpper === 'VOUCHER') {
        operations.push(
          db.staff.update({
            where: { staffCode },
            data: {
              mealVoucher: {
                decrement: amount,
              },
            },
          })
        );
      }
    }

    const orderCreation = db.cafeteriaOrder.create({
      data: {
        ...order,
        orderNumber,
        totalAmount: amount,
        paymentType: method.toUpperCase(),
        orderType: type.toUpperCase(),
        locationId: Number(requesterLocationId),
        staffId: staffRecord?.id || null,
        orderItems: {
          create: cart.map((item: any) => ({
            menuItemId: Number(item.id),
            quantity: Number(item.quantity),
            unitPrice: new decimal(item.price),
            totalPrice: new decimal(Number(item.quantity) * Number(item.price)),
          })),
        },
      },
    });

    operations.push(orderCreation);

    const transactionCreation = db.transaction.create({
      data: {
        amount: amount,
        status: 'SUCCESS',
        // role: 'STAFF',
        type: 'PURCHASE',
        staffId: staffRecord?.id,
        locationId: Number(requesterLocationId),
        mode: method.toLowerCase(),
        reference: orderNumber,
        remarks: `Cafeteria order - ${orderNumber}`,
        fromAccountId: staffRecord?.account?.id,
        toAccountId: cafeteriaSystemAccount?.account?.id,
      },
    });
    operations.push(transactionCreation);

    if (staffRecord && staffRecord.account) {
      const staffUpdate = db.staff.update({
        where: { id: staffRecord.id },
        data: {
          locked: true,
        },
      });
      operations.push(staffUpdate);
    }

    // Execute all operations transactionally
    const results = await db.$transaction(operations);

    // Notify staff who placed the order
    if (staffRecord) {
      await notificationService.createNotificationWithSocket(
        staffRecord.id,
        'Order Created',
        `Your cafeteria order ${orderNumber} has been placed successfully.`,
        'order',
        'medium',
        {
          orderNumber,
          totalAmount: amount.toString(),
          paymentType: method.toUpperCase(),
          orderType: type.toUpperCase(),
        }
      );
    }

    return { message: 'Order created successfully' };
  },

  getOrderById: async (staffId: any, id: string) => {
    await staffHasPermission(staffId, PERMISSIONS.CAFETERIA_ORDERS_MANAGE);
    const order = await db.cafeteriaOrder.findUnique({
      where: { id },
      include: {
        orderItems: {
          include: {
            menuItem: true,
          },
        },
        staff: true,
      },
    });
    if (!order) {
      throw new HttpError('Order not found', 404);
    }
    return order;
  },

  getAllOrders: async (staffId: any, query: any = {}) => {
    const auth = createStaffAuthHelper(staffId);

    const locationId = await auth.getLocationId();

    const page: number = parseInt(query.page as string) || 1;
    const limit: number = parseInt(query.limit as string) || 10;
    const search: string = (query.search as string) || '';
    const paymentType = query.paymentType as string;
    const startDate = query.startDate as string;
    const endDate = query.endDate as string;
    const exportFormat = query.export as string;
    const dateFilter = createDateFilter(startDate, endDate);

    // Create date filter for stats - prioritize startDate/endDate over month/year
    let statsDateFilter = {};
    let selectedYear, selectedMonth;

    if (startDate || endDate) {
      statsDateFilter = createDateFilter(startDate, endDate);
    } else {
      selectedYear = query?.year;
      selectedMonth = query?.month;
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;

      if (!selectedYear || isNaN(Number(selectedYear))) {
        selectedYear = currentYear;
      } else {
        selectedYear = Number(selectedYear);
      }

      if (
        !selectedMonth ||
        isNaN(Number(selectedMonth)) ||
        Number(selectedMonth) < 1 ||
        Number(selectedMonth) > 12
      ) {
        selectedMonth = currentMonth;
      } else {
        selectedMonth = Number(selectedMonth);
      }

      const monthStart = dayjs(`${selectedYear}-${selectedMonth}-01`)
        .startOf('month')
        .toDate();
      const monthEnd = dayjs(`${selectedYear}-${selectedMonth}-01`)
        .endOf('month')
        .toDate();

      statsDateFilter = {
        createdAt: {
          gte: monthStart,
          lte: monthEnd,
        },
      };
    }

    const matchingPaymentTypes = Object.values(PaymentType).filter((type) =>
      type.toLowerCase().includes(search.toLowerCase())
    );

    const searchFilter = search
      ? {
          OR: [
            {
              patientRoomNo: {
                contains: search,
                mode: Prisma.QueryMode.insensitive,
              },
            },
            {
              customerName: {
                contains: search,
                mode: Prisma.QueryMode.insensitive,
              },
            },
            {
              customerPhone: {
                contains: search,
                mode: Prisma.QueryMode.insensitive,
              },
            },
            ...(matchingPaymentTypes.length > 0
              ? [{ paymentType: { in: matchingPaymentTypes as any } }]
              : []),
          ],
        }
      : {};

    const overallWhereClause = {
      staffId: Number(staffId),
      ...(locationId !== undefined && locationId !== null
        ? { locationId }
        : {}),
    };

    const baseWhereClause = {
      staffId: Number(staffId),
      ...(paymentType ? { paymentType: paymentType.toUpperCase() as any } : {}),
      ...(locationId !== undefined && locationId !== null
        ? { locationId }
        : {}),
      ...searchFilter,
    };

    const whereClause = {
      ...baseWhereClause,
      ...(startDate || endDate
        ? dateFilter
        : query.month || query.year
          ? statsDateFilter
          : statsDateFilter),
    };

    // Handle CSV export
    if (exportFormat === 'csv') {
      const orders = await db.cafeteriaOrder.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
        include: {
          orderItems: {
            include: {
              menuItem: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
      });

      const flattenedOrders = orders.map((order: any) => ({
        orderNumber: order.orderNumber,
        customerName: order.customerName || 'N/A',
        customerPhone: order.customerPhone || 'N/A',
        patientRoomNo: order.patientRoomNo || 'N/A',
        orderType: order.orderType,
        paymentType: order.paymentType,
        totalAmount: order.totalAmount,
        createdAt: order.createdAt,
        items: order.orderItems
          .map(
            (item: any) =>
              `${item.menuItem.name} (${item.quantity}x${item.unitPrice})`
          )
          .join('; '),
        itemsCount: order.orderItems.length,
      }));

      return createCSVExport(flattenedOrders, 'my_orders', {
        ...CSV_CONFIGS.orders,
        headers: [
          'orderNumber',
          'customerName',
          'customerPhone',
          'patientRoomNo',
          'orderType',
          'paymentType',
          'totalAmount',
          'createdAt',
          'items',
          'itemsCount',
        ],
      });
    }

    const [orders, totalCount, overallOrders, totalAmount] =
      await db.$transaction([
        db.cafeteriaOrder.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          skip: (page - 1) * limit,
          take: limit,
          include: {
            orderItems: {
              include: {
                menuItem: true,
              },
            },
          },
        }),
        db.cafeteriaOrder.count({
          where: whereClause,
        }),
        db.cafeteriaOrder.count({
          where: overallWhereClause,
        }),
        db.cafeteriaOrder.aggregate({
          _sum: {
            totalAmount: true,
          },
          where: whereClause,
        }),
      ]);

    const response: any = {
      orders: orders,
      totalPages: Math.ceil(totalCount / limit),
      totalCount: totalCount,
      currentPage: page,
      limit: limit,
      overallOrders,
      totalOrders: totalCount,
      totalAmount: totalAmount._sum?.totalAmount || 0,
    };

    return response;
  },

  getAllStaffOrders: async (staffId: any, query: any = {}) => {
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(
      PERMISSIONS.CAFETERIA_ORDERS_MANAGE
    );
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }

    const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
    const locationId = hasLocationAll ? undefined : await auth.getLocationId();

    const page: number = parseInt(query.page as string) || 1;
    const limit: number = parseInt(query.limit as string) || 10;
    const search: string = (query.search as string) || '';
    const paymentType = query.paymentType as string;
    const startDate = query.startDate as string;
    const endDate = query.endDate as string;
    const staff = query.staff;
    const exportFormat = query.export as string;
    const dateFilter = createDateFilter(startDate, endDate);

    // Create stats date filter
    let selectedYear = query?.year;
    let selectedMonth = query?.month;
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;

    let statsDateFilter = {};
    if (startDate || endDate) {
      statsDateFilter = createDateFilter(startDate, endDate);
    } else if (selectedYear || selectedMonth) {
      if (!selectedYear || isNaN(Number(selectedYear))) {
        selectedYear = currentYear;
      } else {
        selectedYear = Number(selectedYear);
      }

      if (
        !selectedMonth ||
        isNaN(Number(selectedMonth)) ||
        Number(selectedMonth) < 1 ||
        Number(selectedMonth) > 12
      ) {
        selectedMonth = currentMonth;
      } else {
        selectedMonth = Number(selectedMonth);
      }

      const monthStart = dayjs(`${selectedYear}-${selectedMonth}-01`)
        .startOf('month')
        .toDate();
      const monthEnd = dayjs(`${selectedYear}-${selectedMonth}-01`)
        .endOf('month')
        .toDate();

      statsDateFilter = {
        createdAt: {
          gte: monthStart,
          lte: monthEnd,
        },
      };
    }

    const todayStart = dayjs().startOf('day').toDate();
    const todayEnd = dayjs().endOf('day').toDate();

    const matchingPaymentTypes = Object.values(PaymentType).filter((type) =>
      type.toLowerCase().includes(search.toLowerCase())
    );

    const searchFilter = search
      ? {
          OR: [
            {
              patientRoomNo: {
                contains: search,
                mode: Prisma.QueryMode.insensitive,
              },
            },
            {
              customerName: {
                contains: search,
                mode: Prisma.QueryMode.insensitive,
              },
            },
            {
              customerPhone: {
                contains: search,
                mode: Prisma.QueryMode.insensitive,
              },
            },
            ...(matchingPaymentTypes.length > 0
              ? [{ paymentType: { in: matchingPaymentTypes as any } }]
              : []),
            {
              staff: {
                OR: [
                  {
                    fullName: {
                      contains: search,
                      mode: Prisma.QueryMode.insensitive,
                    },
                  },
                  {
                    staffCode: {
                      contains: search,
                      mode: Prisma.QueryMode.insensitive,
                    },
                  },
                ],
              },
            },
          ],
        }
      : {};

    const overallWhereClause = {
      ...(staff ? { staffId: Number(staff) } : {}),
      ...(locationId !== undefined && locationId !== null
        ? { locationId }
        : {}),
    };

    const baseWhereClause = {
      ...(paymentType ? { paymentType: paymentType.toUpperCase() as any } : {}),
      ...(staff ? { staffId: Number(staff) } : {}),
      ...(locationId !== undefined && locationId !== null
        ? { locationId }
        : {}),
      ...searchFilter,
    };

    const defaultDailyFilter = {
      createdAt: {
        gte: todayStart,
        lte: todayEnd,
      },
    };

    const whereClause = {
      ...baseWhereClause,
      ...(startDate || endDate
        ? dateFilter
        : query.month || query.year
          ? statsDateFilter
          : defaultDailyFilter),
    };

    const statsWhereClause = {
      ...baseWhereClause,
      ...statsDateFilter,
    };

    // Handle CSV export
    if (exportFormat === 'csv') {
      const orders = await db.cafeteriaOrder.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
        include: {
          orderItems: {
            include: {
              menuItem: {
                select: {
                  name: true,
                },
              },
            },
          },
          staff: {
            select: {
              id: true,
              fullName: true,
              staffCode: true,
            },
          },
        },
      });

      const flattenedOrders = orders.map((order: any) => ({
        orderNumber: order.orderNumber,
        staffName: order.staff?.fullName || 'N/A',
        staffCode: order.staff?.staffCode || 'N/A',
        customerName: order.customerName || 'N/A',
        customerPhone: order.customerPhone || 'N/A',
        patientRoomNo: order.patientRoomNo || 'N/A',
        orderType: order.orderType,
        paymentType: order.paymentType,
        totalAmount: order.totalAmount,
        createdAt: order.createdAt,
        items: order.orderItems
          .map(
            (item: any) =>
              `${item.menuItem.name} (${item.quantity}x${item.unitPrice})`
          )
          .join('; '),
        itemsCount: order.orderItems.length,
      }));

      return createCSVExport(flattenedOrders, 'all_staff_orders', {
        ...CSV_CONFIGS.orders,
        headers: [
          'orderNumber',
          'staffName',
          'staffCode',
          'customerName',
          'customerPhone',
          'patientRoomNo',
          'orderType',
          'paymentType',
          'totalAmount',
          'createdAt',
          'items',
          'itemsCount',
        ],
      });
    }

    const [orders, totalCount, overallOrders, totalAmount] =
      await db.$transaction([
        db.cafeteriaOrder.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          skip: (page - 1) * limit,
          take: limit,
          include: {
            orderItems: {
              include: {
                menuItem: true,
              },
            },
            staff: {
              select: {
                id: true,
                fullName: true,
                staffCode: true,
              },
            },
          },
        }),
        db.cafeteriaOrder.count({
          where: whereClause,
        }),
        db.cafeteriaOrder.count({
          where: overallWhereClause,
        }),
        db.cafeteriaOrder.aggregate({
          _sum: {
            totalAmount: true,
          },
          where: whereClause,
        }),
      ]);

    const response: any = {
      orders: orders,
      totalPages: Math.ceil(totalCount / limit),
      totalCount: totalCount,
      currentPage: page,
      limit: limit,
      overallOrders,
      totalOrders: totalCount,
      totalAmount: totalAmount._sum?.totalAmount || 0,
    };

    return response;
  },
};

export const specialOrderService = {
  createSpecialOrder: async (staffId: any, reqBody: any) => {
    const auth = createStaffAuthHelper(staffId);

    const locationId = await auth.getLocationId();

    const { cart, ...order } = reqBody;

    const existingMenu = await db.cafeteriaSpecialOrder.findFirst({
      where: {
        staffId: Number(staffId),
        status: 'PENDING',
      },
    });

    if (existingMenu) {
      throw new HttpError('You have a pending special order request', 400);
    }
    const orderNumber = `SPORD-0${locationId}${generateOrderNumber()}`;

    const specialOrder = await db.cafeteriaSpecialOrder.create({
      data: {
        ...order,
        orderNumber: orderNumber,
        staffId: Number(staffId),
        locationId: Number(locationId),
        orderItems: {
          create: cart.map((item: any) => ({
            menuItemId: Number(item.id),
            quantity: Number(item.quantity),
            unitPrice: Number(item.price),
            totalPrice: Number(item.quantity) * Number(item.price),
          })),
        },
      },
      include: {
        staff: {
          select: {
            fullName: true,
          },
        },
      },
    });

    return { message: 'Special Menu item created successfully' };
  },
  getAllSpecialOrders: async (staffId: any, query: any = {}) => {
    const auth = createStaffAuthHelper(staffId);

    const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
    const canManageOrders = await auth.hasPermission(
      PERMISSIONS.CAFETERIA_ORDERS_MANAGE
    );

    const page: number = parseInt(query.page as string);
    const limit: number = parseInt(query.limit as string);
    const search: string = (query.search as string) || '';
    const startDate = query.startDate as string;
    const endDate = query.endDate as string;
    const dateFilter = createDateFilter(startDate, endDate);

    let whereClause: any = {
      ...(search
        ? {
            OR: [
              { orderNumber: { contains: search, mode: 'insensitive' } },
              { purpose: { contains: search, mode: 'insensitive' } },
            ],
          }
        : {}),
      ...dateFilter,
    };

    if (hasLocationAll) {
    } else if (canManageOrders) {
      const locationId = await auth.getLocationId();
      const location = await db.location.findUnique({
        where: { id: Number(locationId) },
        select: { regionId: true },
      });

      whereClause.location = {
        regionId: location?.regionId,
      };
    } else {
      // Load orders by locationId only
      const locationId = await auth.getLocationId();
      whereClause.locationId = Number(locationId);
    }

    const [orders, totalCount] = await db.$transaction([
      db.cafeteriaSpecialOrder.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          orderItems: {
            include: {
              menuItem: true,
            },
          },
          returnedItems: {
            include: {
              menuItem: true,
            },
          },
          staff: {
            select: {
              fullName: true,
            },
          },
        },
      }),
      db.cafeteriaSpecialOrder.count({
        where: whereClause,
      }),
    ]);

    const response = {
      specialOrders: orders,
      totalPages: Math.ceil(totalCount / limit),
      totalCount: totalCount,
      currentPage: page,
      limit: limit,
    };

    return response;
  },
  updateSpecialOrder: async (staffId: any, reqBody: any) => {
    const auth = createStaffAuthHelper(staffId);

    const canApprove = await auth.hasPermission(
      PERMISSIONS.CAFETERIA_SPECIAL_APPROVE
    );

    const canManage = await auth.hasPermission(
      PERMISSIONS.CAFETERIA_ORDERS_MANAGE
    );

    const {
      orderId,
      comment,
      approvedBy,
      declinedBy,
      deliveredBy,
      status: requestedStatus,
    } = reqBody;

    const order = await db.cafeteriaSpecialOrder.findUnique({
      where: { id: orderId },
    });

    if (!order) {
      throw new HttpError('Order not found', 404);
    }

    const currentStatus = order.status;

    // Handle PENDING status
    if (currentStatus === 'PENDING') {
      if (!['APPROVED', 'DECLINED'].includes(requestedStatus)) {
        throw new HttpError('Invalid status transition from PENDING', 400);
      }
      if (!canApprove) {
        throw new HttpError('Unauthorized to approve or decline', 403);
      }
    }
    // Handle APPROVED status
    else if (currentStatus === 'APPROVED') {
      if (requestedStatus !== 'DELIVERED') {
        throw new HttpError('Invalid status transition from APPROVED', 400);
      }
      if (!canManage) {
        throw new HttpError('Unauthorized to mark as delivered', 403);
      }
    }
    // Handle DELIVERED status
    else if (currentStatus === 'DELIVERED') {
      if (requestedStatus !== 'RECEIVED') {
        throw new HttpError('Invalid status transition from DELIVERED', 400);
      }
      // Staff who ordered can receive their own order
      if (order.staffId !== Number(staffId)) {
        throw new HttpError(
          'Only the ordering staff can receive this order',
          403
        );
      }
    } else {
      throw new HttpError('This order status cannot be changed', 400);
    }

    // Perform the update
    await db.cafeteriaSpecialOrder.update({
      where: { id: orderId },
      data: {
        approvedBy,
        declinedBy,
        deliveredBy,
        status: requestedStatus,
        ...(comment && { comment }),
      },
    });

    // Notify requesting staff about status update
    await notificationService.createNotificationWithSocket(
      order.staffId,
      'Special Order Updated',
      `Your special order ${order.orderNumber} status has been updated to ${requestedStatus}.`,
      'order',
      'medium',
      {
        orderId,
        orderNumber: order.orderNumber,
        status: requestedStatus,
        comment,
      }
    );

    return { message: 'Order status updated successfully' };
  },

  receiveSpecialOrder: async (staffId: any, reqBody: any) => {
    const { orderId, receivedBy, totalAmount, receivedItems } = reqBody;

    const order = await db.cafeteriaSpecialOrder.findUnique({
      where: { id: orderId },
      include: { orderItems: true },
    });

    if (!order) {
      throw new HttpError('Order not found', 404);
    }

    if (order.status !== 'DELIVERED') {
      throw new HttpError(
        'Order must be delivered before it can be received',
        400
      );
    }

    await db.$transaction(async (tx) => {
      // Update order status and received info
      await tx.cafeteriaSpecialOrder.update({
        where: { id: orderId },
        data: {
          status: 'RECEIVED',
          receivedBy: receivedBy,
          receivedAt: new Date(),
          receivedTotalAmount: totalAmount,
        },
      });

      // Update received quantities for each item
      for (const item of receivedItems) {
        await tx.cafeteriaOrderItem.updateMany({
          where: {
            specialOrderId: orderId,
          },
          data: {
            receivedQuantity: item.quantity,
            totalPrice: item.quantity * item.unitPrice,
          },
        });
      }

      const cafeteriaSystemAccount = await getCafeteriaSystemAccount();
      const companySystemAccount = await getMainSystemAccount();

      // Create transaction record
      await tx.transaction.create({
        data: {
          amount: new decimal(totalAmount),
          status: 'SUCCESS',
          type: 'PURCHASE',
          locationId: Number(order.locationId),
          mode: 'Special',
          reference: order.orderNumber,
          remarks: `Special order received - ${order.purpose}`,
          fromAccountId: companySystemAccount?.account?.id,
          toAccountId: cafeteriaSystemAccount?.account?.id,
        },
      });
    });

    return { message: 'Order received successfully' };
  },

  returnSpecialOrderItems: async (staffId: any, reqBody: any) => {
    const { orderId, returnedBy, totalAmount, returnedItems } = reqBody;

    const order = await db.cafeteriaSpecialOrder.findUnique({
      where: { id: orderId },
      include: { orderItems: true },
    });

    if (!order) {
      throw new HttpError('Order not found', 404);
    }

    if (order.status !== 'RECEIVED') {
      throw new HttpError(
        'Order must be received before items can be returned',
        400
      );
    }

    await db.$transaction(async (tx) => {
      const returnedItemsDetails = [];

      for (const item of returnedItems) {
        const orderItem = order.orderItems.find(
          (oi) => oi.menuItemId === item.id
        );

        if (!orderItem) {
          throw new HttpError(`Menu item ${item.id} not found in order`, 400);
        }

        const receivedQty = orderItem.receivedQuantity || 0;
        if (item.quantity > receivedQty) {
          throw new HttpError(
            `Cannot return ${item.quantity} items. Only ${receivedQty} were received`,
            400
          );
        }

        // Record the return
        await tx.cafeteriaReturnedItem.create({
          data: {
            specialOrderId: orderId,
            menuItemId: Number(item.id),
            returnedQuantity: item.quantity,
            reason: item.reason,
            returnedBy: returnedBy,
          },
          include: {
            menuItem: true,
          },
        });

        returnedItemsDetails.push(item.reason);
      }

      const transaction = await tx.transaction.findFirst({
        where: {
          reference: order.orderNumber,
        },
      });

      // Update transaction record with returned amount
      if (transaction) {
        const returnedItemsList = returnedItemsDetails.join(', ');
        await tx.transaction.update({
          where: {
            id: transaction.id,
          },
          data: {
            amount: new decimal(totalAmount),
            remarks: `${transaction.remarks || ''}\nReturn reason: ${returnedItemsList}. Amount: ${totalAmount}`,
          },
        });
      }
    });

    return { message: 'Items returned successfully' };
  },

  getStaffSpecialOrders: async (staffId: any, query: any = {}) => {
    const page: number = parseInt(query.page as string) || 1;
    const limit: number = parseInt(query.limit as string) || 10;
    const search: string = (query.search as string) || '';
    const startDate = query.startDate as string;
    const endDate = query.endDate as string;
    const dateFilter = createDateFilter(startDate, endDate);

    const whereClause: any = {
      staffId: Number(staffId),
      ...(search
        ? {
            OR: [
              { orderNumber: { contains: search, mode: 'insensitive' } },
              { purpose: { contains: search, mode: 'insensitive' } },
            ],
          }
        : {}),
      ...dateFilter,
    };

    const [orders, totalCount] = await db.$transaction([
      db.cafeteriaSpecialOrder.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          orderItems: {
            include: {
              menuItem: true,
            },
          },
          returnedItems: {
            include: {
              menuItem: true,
            },
          },
        },
      }),
      db.cafeteriaSpecialOrder.count({
        where: whereClause,
      }),
    ]);

    return {
      specialOrders: orders,
      totalPages: Math.ceil(totalCount / limit),
      totalCount: totalCount,
      currentPage: page,
      limit: limit,
    };
  },
};
