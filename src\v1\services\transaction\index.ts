import { db, decimal } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import {
  staffHasPermission,
  PERMISSIONS,
  createStaffAuthHelper,
} from '../../utils/permission';
import {
  transactionStatus,
  transactionType,
  createDateFilter,
  generateOrderNumber,
} from '../../utils/util';
import * as bcrypt from 'bcryptjs';
import { enqueueSendEmailJob } from '../../jobs/queueJobs/queues/emailQueueJob';
import dayjs from 'dayjs';
import { sendToUser, broadcast } from '../socket';
import { NotificationService } from '../notification/notificationService';

const getMainSystemAccount = async () => {
  return await db.systemAccount.findFirst({
    where: {
      type: 'COMPANY',
    },
    include: {
      account: true,
    },
  });
};

export const transactionService = {
  getAllTransactions: async (staffId: any, query: any) => {
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(PERMISSIONS.TRANSACTION_EDIT);
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }

    const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
    const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

    let locationFilter: any = {};
    if (hasLocationAll) {
      // No location filter - get all data
    } else if (hasRegion) {
      const regionId = await auth.getRegionId();
      locationFilter = {
        location: {
          regionId,
        },
      };
    } else {
      const locationId = await auth.getLocationId();
      locationFilter = { locationId };
    }

    // Parse pagination parameters with defaults
    const page: number = parseInt(query.page as string) || 1;
    const limit: number = parseInt(query.limit as string) || 10;

    // Parse filter parameters
    const status = query.status;
    const type = query.type;
    const search = query.search as string;
    const startDate = query.startDate as string;
    const endDate = query.endDate as string;

    // Validate filter parameters
    if (status && !transactionStatus.includes(status.toUpperCase())) {
      throw new HttpError('Invalid transaction status', 400);
    }
    if (type && !transactionType.includes(type.toUpperCase())) {
      throw new HttpError('Invalid transaction type', 400);
    }

    // Create date filter using the reusable function
    const dateFilter = createDateFilter(startDate, endDate);

    // Build where clause with all filters
    const whereClause: any = {
      ...(search
        ? {
            OR: [
              { reference: { contains: search, mode: 'insensitive' } },
              { mode: { contains: search, mode: 'insensitive' } },
              {
                fromAccount: {
                  OR: [
                    {
                      user: {
                        OR: [
                          {
                            emailAddress: {
                              contains: search,
                              mode: 'insensitive',
                            },
                          },
                          {
                            phoneNumber: {
                              contains: search,
                              mode: 'insensitive',
                            },
                          },
                          { uhid: { contains: search, mode: 'insensitive' } },
                        ],
                      },
                    },
                    {
                      staff: {
                        OR: [
                          { email: { contains: search, mode: 'insensitive' } },
                          {
                            phoneNumber: {
                              contains: search,
                              mode: 'insensitive',
                            },
                          },
                          {
                            fullName: { contains: search, mode: 'insensitive' },
                          },
                        ],
                      },
                    },
                  ],
                },
              },
              {
                toAccount: {
                  OR: [
                    {
                      user: {
                        OR: [
                          {
                            emailAddress: {
                              contains: search,
                              mode: 'insensitive',
                            },
                          },
                          {
                            phoneNumber: {
                              contains: search,
                              mode: 'insensitive',
                            },
                          },
                          { uhid: { contains: search, mode: 'insensitive' } },
                        ],
                      },
                    },
                    {
                      staff: {
                        OR: [
                          { email: { contains: search, mode: 'insensitive' } },
                          {
                            phoneNumber: {
                              contains: search,
                              mode: 'insensitive',
                            },
                          },
                          {
                            fullName: { contains: search, mode: 'insensitive' },
                          },
                        ],
                      },
                    },
                  ],
                },
              },
            ],
          }
        : {}),
      ...(status ? { status: status.toUpperCase() } : {}),
      ...(type ? { type: type.toUpperCase() } : {}),
      ...dateFilter,
      ...locationFilter,
    };

    const [transactions, totalPages, totalCount] = await db.$transaction([
      db.transaction.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
        include: {
          fromAccount: {
            select: {
              user: {
                select: {
                  emailAddress: true,
                  phoneNumber: true,
                  firstName: true,
                  uhid: true,
                },
              },
              staff: {
                select: {
                  fullName: true,
                  staffCode: true,
                },
              },
              system: {
                select: {
                  name: true,
                },
              },
            },
          },
          toAccount: {
            select: {
              user: {
                select: {
                  emailAddress: true,
                  phoneNumber: true,
                  firstName: true,
                  uhid: true,
                },
              },
              staff: {
                select: {
                  fullName: true,
                  staffCode: true,
                },
              },
              system: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      db.transaction.count({
        where: whereClause,
      }),
      db.transaction.count(),
    ]);

    return {
      transactions: transactions,
      totalPages: Math.ceil(totalPages / limit),
      totalCount: totalCount,
      currentPage: page,
      limit: limit,
    };
  },

  getStaffTransactions: async (staffId: any, query: any) => {
    const page: number = parseInt(query.page as string) || 1;
    const limit: number = parseInt(query.limit as string) || 10;
    const status = query.status;
    const type = query.type;
    const search = query.search as string;
    const startDate = query.startDate as string;
    const endDate = query.endDate as string;

    if (status && !transactionStatus.includes(status.toUpperCase())) {
      throw new HttpError('Invalid transaction status', 400);
    }
    if (type && !transactionType.includes(type.toUpperCase())) {
      throw new HttpError('Invalid transaction type', 400);
    }

    const dateFilter = createDateFilter(startDate, endDate);

    const whereClause: any = {
      OR: [
        {
          fromAccount: {
            staffId: Number(staffId),
          },
        },
        {
          toAccount: {
            staffId: Number(staffId),
          },
        },
      ],
      ...(search
        ? {
            AND: [
              {
                OR: [
                  { reference: { contains: search, mode: 'insensitive' } },
                  { mode: { contains: search, mode: 'insensitive' } },
                ],
              },
            ],
          }
        : {}),
      ...(status ? { status: status.toUpperCase() } : {}),
      ...(type ? { type: type.toUpperCase() } : {}),
      ...dateFilter,
    };

    const [transactions, totalPages, totalCount] = await db.$transaction([
      db.transaction.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
        include: {
          fromAccount: {
            select: {
              staff: {
                select: {
                  fullName: true,
                  staffCode: true,
                },
              },
              system: {
                select: {
                  name: true,
                },
              },
            },
          },
          toAccount: {
            select: {
              staff: {
                select: {
                  fullName: true,
                  staffCode: true,
                },
              },
              system: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      db.transaction.count({
        where: whereClause,
      }),
      db.transaction.count({
        where: {
          OR: [
            { fromAccount: { staffId: Number(staffId) } },
            { toAccount: { staffId: Number(staffId) } },
          ],
        },
      }),
    ]);

    return {
      transactions: transactions,
      totalPages: Math.ceil(totalPages / limit),
      totalCount: totalCount,
      currentPage: page,
      limit: limit,
    };
  },
  staffWithdrawal: async (staffId: any, reqBody: any) => {
    const { amount, details, password } = reqBody;
    const newAmount = new decimal(amount);
    const charges = newAmount.mul(0.05);
    const checkStaff = await db.staff.findUnique({
      where: { id: Number(staffId) },
      include: { account: true },
    });

    if (!checkStaff) {
      throw new HttpError('Staff does not exist', 400);
    }
    if (new decimal(checkStaff.wallet).lessThan(newAmount)) {
      throw new HttpError('Insufficient funds in wallet for withdrawal', 400);
    }

    const existingWithdrawal = await db.transaction.findFirst({
      where: {
        toAccount: { staffId: Number(staffId) },
        type: 'WITHDRAWAL',
        status: {
          in: ['PENDING', 'PROCESSING'],
        },
      },
    });
    if (existingWithdrawal) {
      throw new HttpError(
        'Pending withdrawal request found. Please wait for it to be processed.',
        400
      );
    }

    // const existingCode = await db.confirmationCode.findUnique({
    //   where: { assignedToStaffId: Number(id) }
    // });

    // if (existingCode && existingCode.attempts >= existingCode.maxAttempt) {
    //   throw new HttpError('Maximum OTP attempts exceeded, Kindly contact software team', 400);
    // }

    const verifyPassword = await bcrypt.compare(
      password,
      checkStaff.password || ''
    );
    if (!verifyPassword) {
      throw new HttpError('The password entered is not the correct', 400);
    }

    // const confirmCode = generateCode(6);

    //  await db.confirmationCode.upsert({
    //   where: { assignedToStaffId: Number(id) },
    //   create: {
    //     code: confirmCode,
    //     assignedToStaffId: Number(id),
    //     attempts: 1,
    //   },
    //   update: {
    //     code: confirmCode,
    //     attempts: { increment: 1 },
    //   }
    //  })

    //  const mailOptions = {
    //    from: '"Cedarcrest Hospitals Innovations" <<EMAIL>>',
    //    to: checkStaff.email,
    //    subject: 'Your One-Time-Password(OTP)',
    //    template: 'otp',
    //    context: {
    //      otp: confirmCode,
    //      name: checkStaff.fullName,
    //    },
    //  };
    //  enqueueSendEmailJob(mailOptions);

    const companyAccount = await getMainSystemAccount();

    const transaction = await db.transaction.create({
      data: {
        reference: `WID-${checkStaff.locationId}${generateOrderNumber()}`,
        amount: newAmount,
        charges: charges,
        balance: newAmount.minus(charges),
        mode: 'Withdrawal',
        type: 'WITHDRAWAL',
        status: 'PENDING',
        fromAccountId: companyAccount?.account?.id || null,
        toAccountId: checkStaff.account?.id || null,
        remarks: details,
      },
    });

    // Create notification with socket emission
    await NotificationService.createNotificationWithSocket(
      Number(staffId),
      'Withdrawal Request Created',
      `Your withdrawal request of ${newAmount} has been submitted and is pending approval.`,
      'transaction',
      'medium',
      {
        transactionId: transaction.id,
        reference: transaction.reference,
        amount: newAmount.toString(),
        type: 'WITHDRAWAL',
      }
    );

    return { message: 'Withdrawal request created successfully' };
  },

  staffFundWithdrawalUpdate: async (staffId: any, reqBody: any) => {
    const auth = createStaffAuthHelper(staffId);
    const { transactionId, status: rawStatus, remarks } = reqBody;
    const status = rawStatus?.toUpperCase();

    const canManage = await auth.hasPermission(PERMISSIONS.TRANSACTION_EDIT);
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }

    const transaction = await db.transaction.findUnique({
      where: { id: transactionId },
      include: {
        toAccount: {
          include: {
            staff: {
              include: {
                location: true,
              },
            },
          },
        },
      },
    });

    if (!transaction) {
      throw new HttpError('Transaction not found', 404);
    }

    const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
    const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

    if (!hasLocationAll) {
      if (hasRegion) {
        const regionId = await auth.getRegionId();
        if (transaction.toAccount?.staff?.location?.regionId !== regionId) {
          throw new HttpError('Unauthorized to update this transaction', 403);
        }
      } else {
        const locationId = await auth.getLocationId();
        if (transaction.toAccount?.staff?.locationId !== locationId) {
          throw new HttpError('Unauthorized to update this transaction', 403);
        }
      }
    }

    const currentStatus = transaction.status;

    if (currentStatus === 'PENDING' && status === 'PROCESSING') {
      const currentWallet = new decimal(
        transaction.toAccount?.staff?.wallet || 0
      );

      if (currentWallet.lessThan(transaction.amount)) {
        await db.transaction.update({
          where: { id: transactionId },
          data: {
            status: 'CANCELLED',
            remarks: `${transaction.remarks || ''}\n - Cancelled: Insufficient wallet balance at processing time`,
          },
        });

        // Create notification with socket emission
        await NotificationService.createNotificationWithSocket(
          transaction.toAccount?.staff?.id!,
          'Withdrawal Cancelled',
          'Your withdrawal request has been cancelled due to insufficient wallet balance.',
          'transaction',
          'high',
          {
            transactionId,
            status: 'CANCELLED',
            reason: 'Insufficient wallet balance',
          }
        );
      } else {
        await db.$transaction(async (tx) => {
          await tx.transaction.update({
            where: { id: transactionId },
            data: {
              status: 'PROCESSING',
              remarks: `${transaction.remarks || ''}\n - Processing: ${remarks}`,
            },
          });

          await tx.staff.update({
            where: { id: transaction.toAccount?.staff?.id },
            data: {
              wallet: {
                decrement: transaction.amount,
              },
            },
          });
        });

        // Create notification with socket emission
        await NotificationService.createNotificationWithSocket(
          transaction.toAccount?.staff?.id!,
          'Withdrawal Processing',
          `Your withdrawal request of ${transaction.amount} is now being processed.`,
          'transaction',
          'medium',
          {
            transactionId,
            status: 'PROCESSING',
            amount: transaction.amount.toString(),
          }
        );
      }
    } else if (
      currentStatus === 'PROCESSING' &&
      ['FAILED', 'CANCELLED', 'SUCCESS'].includes(status)
    ) {
      if (status !== 'SUCCESS') {
        await db.$transaction(async (tx) => {
          await tx.transaction.update({
            where: { id: transactionId },
            data: {
              status,
              remarks: `${transaction.remarks || ''}\n -${status}: ${remarks}`,
            },
          });

          await tx.staff.update({
            where: { id: transaction.toAccount?.staff?.id },
            data: {
              wallet: {
                increment: transaction.amount,
              },
            },
          });
        });

        // Emit socket notification
        sendToUser(transaction.toAccount?.staff?.id!, 'withdrawal_status_updated', {
          transactionId,
          status,
          amount: transaction.amount.toString(),
          refunded: true,
          timestamp: new Date(),
        });
      } else {
        await db.transaction.update({
          where: { id: transactionId },
          data: {
            status: 'SUCCESS',
            remarks: `${transaction.remarks || ''}\n - Success: ${remarks}`,
          },
        });

        // Create notification with socket emission
        await NotificationService.createNotificationWithSocket(
          transaction.toAccount?.staff?.id!,
          'Withdrawal Completed',
          `Your withdrawal of ${transaction.amount} has been completed successfully.`,
          'transaction',
          'medium',
          {
            transactionId,
            amount: transaction.amount.toString(),
            reference: transaction.reference,
            status: 'SUCCESS',
          }
        );
      }
    } else {
      throw new HttpError('Invalid status transition', 400);
    }

    return { message: 'Transaction status updated successfully' };
  },

  staffFundTransfer: async (staffId: any, reqBody: any) => {
    const { amount, toStaffId, password } = reqBody;
    const newAmount = new decimal(amount);
    const checkStaff = await db.staff.findUnique({
      where: { id: Number(staffId) },
      include: { account: true },
    });

    const checReceivingStaff = await db.staff.findUnique({
      where: { staffCode: toStaffId },
      include: { account: true },
    });

    if (!checkStaff) {
      throw new HttpError('Staff does not exist', 400);
    }
    if (!checReceivingStaff) {
      throw new HttpError('receiving staff code appears incorrect', 400);
    }
    if (new decimal(checkStaff.wallet).lessThan(newAmount)) {
      throw new HttpError('Insufficient funds in wallet for transfer', 400);
    }
    const verifyPassword = await bcrypt.compare(
      password,
      checkStaff.password || ''
    );
    if (!verifyPassword) {
      throw new HttpError('The password entered is not the correct', 400);
    }

    const reference = `WID-${checkStaff.locationId}${generateOrderNumber()}`;
    
    await db.$transaction(async (tx) => {
      await tx.staff.update({
        where: { id: Number(staffId) },
        data: {
          wallet: {
            decrement: newAmount,
          },
        },
      });

      await tx.staff.update({
        where: { staffCode: toStaffId },
        data: {
          wallet: {
            increment: newAmount,
          },
        },
      });
      await tx.transaction.create({
      data: {
        reference,
        amount: newAmount,
        mode: 'internal',
        type: 'TRANSFER',
        status: 'SUCCESS',
        fromAccountId: checkStaff.account?.id || null,
        toAccountId: checReceivingStaff.account?.id || null,
        remarks: 'Staff Internal Fund Transfer',
      },
    });
    });

    // Create notifications with socket emissions for both users
    await NotificationService.createNotificationWithSocket(
      Number(staffId),
      'Transfer Sent',
      `You have successfully transferred ${newAmount} to ${checReceivingStaff.fullName}.`,
      'transaction',
      'medium',
      {
        reference,
        amount: newAmount.toString(),
        recipient: checReceivingStaff.fullName,
        recipientCode: toStaffId,
        type: 'TRANSFER_SENT',
      }
    );

    await NotificationService.createNotificationWithSocket(
      checReceivingStaff.id,
      'Transfer Received',
      `You have received ${newAmount} from ${checkStaff.fullName}.`,
      'transaction',
      'medium',
      {
        reference,
        amount: newAmount.toString(),
        sender: checkStaff.fullName,
        senderCode: checkStaff.staffCode,
        type: 'TRANSFER_RECEIVED',
      }
    );

         const mailOptions = {
       from: '"Cedarcrest Hospitals Innovations" <<EMAIL>>',
       to: checkStaff.email,
       subject: 'Wallet Transfer Notification',
       template: 'transfer-notification',
       context: {
         sender_name: checkStaff.fullName,
         recipient_name: checReceivingStaff.fullName,
         amount: newAmount,
        reference,
        timestamp: dayjs(new Date()).format('DD MMMM YYYY, h:mm:ss A')
       },
     };
     enqueueSendEmailJob(mailOptions);
     return { message: 'Transfer successful' };
  },
};
