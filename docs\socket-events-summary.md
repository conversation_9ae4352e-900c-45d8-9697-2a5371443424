# Socket.IO Events Implementation Summary

This document outlines all the socket.io events that have been implemented throughout the application for real-time notifications.

## Transaction Events

### Withdrawal Events
- `withdrawal_request_created` - Emitted when a staff member creates a withdrawal request
- `withdrawal_status_updated` - Emitted when withdrawal status changes (PROCESSING, CANC<PERSON>LED, etc.)
- `withdrawal_completed` - Emitted when withdrawal is successfully completed

### Transfer Events
- `transfer_sent` - Emitted to sender when transfer is completed
- `transfer_received` - Emitted to recipient when they receive a transfer

## Package Booking Events

- `new_package_booking` - Broadcasted when a new package booking is created
- `package_booking_updated` - Broadcasted when booking status is updated
- `package_booking_completed` - Broadcasted when booking is completed with payment
- `package_booking_deleted` - Broadcasted when a booking is deleted

## Cafeteria Events

### Regular Orders
- `cafeteria_order_created` - Sent to staff member who placed the order
- `new_cafeteria_order` - Broadcasted to cafeteria staff for new orders

### Special Orders
- `new_special_order` - Broadcasted when a special order is created
- `special_order_status_updated` - Sent to ordering staff when status changes
- `special_order_updated` - Broadcasted to cafeteria staff for status updates
- `special_order_received` - Sent to staff when they receive their special order
- `special_order_items_returned` - Broadcasted when items are returned

## Staff Events

- `new_staff_created` - Broadcasted when a new staff member is added
- `profile_updated` - Sent to staff when their profile is updated
- `login_successful` - Sent to staff upon successful login

## Game Events

### Game Management
- `game_published` - Broadcasted when a game is published
- `game_completed_with_rewards` - Sent to game participants when game completes
- `reward_won` - Sent to individual winners when they receive rewards

### Existing Game Events (from original implementation)
- `game_started` - Game has started
- `game_update` - General game updates
- `leaderboard_updated` - Leaderboard changes
- `player_joined` - Player joins game
- `player_left` - Player leaves game

## Forum/Messaging Events (Existing Implementation)

### Channel Events
- `new_message` - New message in channel
- `message_updated` - Message edited
- `message_deleted` - Message deleted
- `reaction_added` - Reaction added to message
- `reaction_removed` - Reaction removed from message

### Direct Messages
- `new_direct_message` - New direct message received
- `dm_message_read` - Direct message marked as read
- `dm_user_typing` - User typing in DM
- `dm_user_stopped_typing` - User stopped typing in DM

### User Presence
- `user_online` - User comes online
- `user_offline` - User goes offline
- `user_status_changed` - User status update

## Referral Events

- `new_referral_created` - Broadcasted when a new referral is created
- `referral_status_updated` - Broadcasted when referral status changes
- `referral_comment_added` - Broadcasted when a comment is added to referral

## Notification Events

### General Notifications
- `new_notification` - Sent when a new notification is created
- `notifications_marked_read` - Sent when notifications are marked as read
- `all_notifications_marked_read` - Sent when all notifications are marked as read

### System Notifications
- `system_notification` - System-wide announcements
- `emergency_notification` - Critical/emergency notifications
- `maintenance_notification` - Maintenance announcements

## Socket Utility Functions

The following utility functions are available for emitting events:

### Direct User Communication
- `sendToUser(userId, event, data)` - Send to specific user
- `notifyMultipleUsers(userIds, event, data)` - Send to multiple users

### Broadcasting
- `broadcast(event, data)` - Send to all connected users
- `notifyLocationStaff(locationId, event, data)` - Send to staff in specific location
- `notifyDepartmentStaff(departmentId, event, data)` - Send to staff in specific department
- `notifyByRole(role, event, data)` - Send to users with specific role

### Group Communication
- `sendToChannel(channelId, event, data)` - Send to channel members
- `sendToGroup(groupId, event, data)` - Send to group members
- `sendToGame(gameId, event, data)` - Send to game participants

## Notification Service Integration

The `NotificationService` provides methods that create both database notifications and emit socket events:

- `createNotificationWithSocket()` - Creates DB notification + socket emission
- `socket.*` - Access to all socket notification methods
- `forum.*` - Forum-specific notifications
- `game.*` - Game-specific notifications

## Event Data Structure

All socket events follow a consistent structure:

```typescript
{
  title: string;           // Notification title
  message: string;         // Notification message
  type: 'info' | 'success' | 'warning' | 'error';
  data?: any;             // Additional event-specific data
  timestamp: Date;        // Event timestamp
}
```

## Frontend Integration

The frontend should listen for these events and handle them appropriately:

```javascript
// Example frontend socket listener
socket.on('new_notification', (data) => {
  showNotification(data.title, data.message, data.type);
});

socket.on('withdrawal_completed', (data) => {
  updateWalletBalance();
  showSuccessMessage(data.message);
});
```

## Security Considerations

- All socket events are authenticated using JWT tokens
- Users only receive events they're authorized to see
- Location and role-based filtering is applied where appropriate
- Sensitive data is filtered before emission

## Performance Notes

- Events are emitted asynchronously to avoid blocking operations
- Database notifications are created in transactions where applicable
- Socket emissions include error handling and logging
- Events are cached appropriately to reduce database load