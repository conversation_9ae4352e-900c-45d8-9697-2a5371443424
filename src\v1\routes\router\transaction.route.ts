import { Router } from 'express';
import { secure } from '../../middleware/auth';
import { transactionControllers } from '../../controllers/transaction/transaction.controller';

export const transactionRoute = Router();

transactionRoute.get(
  '/list-all-transaction',
  secure,
  transactionControllers.ListAllTransactions
);

transactionRoute.get(
  '/list-staff-transaction',
  secure,
  transactionControllers.ListAllStaffTransactions
);

transactionRoute.post(
  '/withdrawal-request',
  secure,
  transactionControllers.withdrawalRequest
);


transactionRoute.post(
  '/transfer-funds',
  secure,
  transactionControllers.transferFunds
);

transactionRoute.patch(
  '/withdrawal-request-update',
  secure,
  transactionControllers.withdrawalRequestUpdate
);

// Send meal voucher to staff
transactionRoute.post(
  '/send-voucher',
  secure,
  transactionControllers.SendVoucherHandler
);
